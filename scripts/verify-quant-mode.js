#!/usr/bin/env node

/**
 * Verification script for Roo-Code Quantitative Mode
 * 
 * This script verifies that all quantitative mode components are properly integrated:
 * - Mode configuration
 * - Tool definitions
 * - Tool implementations
 * - Tool routing
 * - Prompt templates
 */

const fs = require('fs');
const path = require('path');

class QuantModeVerifier {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.successes = [];
    }

    log(type, message) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
        
        switch (type) {
            case 'error':
                this.errors.push(message);
                console.error(`❌ ${logMessage}`);
                break;
            case 'warning':
                this.warnings.push(message);
                console.warn(`⚠️  ${logMessage}`);
                break;
            case 'success':
                this.successes.push(message);
                console.log(`✅ ${logMessage}`);
                break;
            default:
                console.log(`ℹ️  ${logMessage}`);
        }
    }

    fileExists(filePath) {
        try {
            return fs.existsSync(filePath);
        } catch (error) {
            return false;
        }
    }

    readFile(filePath) {
        try {
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            this.log('error', `Failed to read file ${filePath}: ${error.message}`);
            return null;
        }
    }

    verifyModeConfiguration() {
        this.log('info', 'Verifying mode configuration...');
        
        const modesFile = 'src/shared/modes.ts';
        if (!this.fileExists(modesFile)) {
            this.log('error', `Modes file not found: ${modesFile}`);
            return false;
        }

        const content = this.readFile(modesFile);
        if (!content) return false;

        // Check for quant mode definition
        if (content.includes('slug: "quant"')) {
            this.log('success', 'Quantitative mode configuration found');
        } else {
            this.log('error', 'Quantitative mode configuration not found');
            return false;
        }

        // Check for quant-tools group
        if (content.includes('"quant-tools"')) {
            this.log('success', 'Quant-tools group reference found in mode');
        } else {
            this.log('warning', 'Quant-tools group not found in mode configuration');
        }

        return true;
    }

    verifyToolDefinitions() {
        this.log('info', 'Verifying tool definitions...');

        const toolsFile = 'src/shared/tools.ts';
        if (!this.fileExists(toolsFile)) {
            this.log('error', `Tools file not found: ${toolsFile}`);
            return false;
        }

        const content = this.readFile(toolsFile);
        if (!content) return false;

        const requiredTools = [
            'generate_strategy',
            'backtest_strategy', 
            'calculate_metrics',
            'optimize_parameters'
        ];

        let allToolsFound = true;
        for (const tool of requiredTools) {
            if (content.includes(`"${tool}"`)) {
                this.log('success', `Tool definition found: ${tool}`);
            } else {
                this.log('error', `Tool definition missing: ${tool}`);
                allToolsFound = false;
            }
        }

        // Check for quant-tools group
        if (content.includes('"quant-tools"')) {
            this.log('success', 'Quant-tools group definition found');
        } else {
            this.log('error', 'Quant-tools group definition missing');
            allToolsFound = false;
        }

        return allToolsFound;
    }

    verifyToolImplementations() {
        this.log('info', 'Verifying tool implementations...');

        const toolFiles = [
            'src/core/tools/generateStrategyTool.ts',
            'src/core/tools/backtestStrategyTool.ts',
            'src/core/tools/calculateMetricsTool.ts',
            'src/core/tools/optimizeParametersTool.ts'
        ];

        let allImplementationsFound = true;
        for (const toolFile of toolFiles) {
            if (this.fileExists(toolFile)) {
                this.log('success', `Tool implementation found: ${path.basename(toolFile)}`);
                
                // Verify function export
                const content = this.readFile(toolFile);
                if (content && content.includes('export async function')) {
                    this.log('success', `Function export verified: ${path.basename(toolFile)}`);
                } else {
                    this.log('error', `Function export missing: ${path.basename(toolFile)}`);
                    allImplementationsFound = false;
                }
            } else {
                this.log('error', `Tool implementation missing: ${toolFile}`);
                allImplementationsFound = false;
            }
        }

        return allImplementationsFound;
    }

    verifyToolRouting() {
        this.log('info', 'Verifying tool routing...');

        const routingFile = 'src/core/assistant-message/presentAssistantMessage.ts';
        if (!this.fileExists(routingFile)) {
            this.log('error', `Routing file not found: ${routingFile}`);
            return false;
        }

        const content = this.readFile(routingFile);
        if (!content) return false;

        const requiredImports = [
            'generateStrategyTool',
            'backtestStrategyTool',
            'calculateMetricsTool', 
            'optimizeParametersTool'
        ];

        const requiredCases = [
            'case "generate_strategy"',
            'case "backtest_strategy"',
            'case "calculate_metrics"',
            'case "optimize_parameters"'
        ];

        let allRoutingFound = true;

        // Check imports
        for (const importName of requiredImports) {
            if (content.includes(importName)) {
                this.log('success', `Tool import found: ${importName}`);
            } else {
                this.log('error', `Tool import missing: ${importName}`);
                allRoutingFound = false;
            }
        }

        // Check switch cases
        for (const caseName of requiredCases) {
            if (content.includes(caseName)) {
                this.log('success', `Tool case found: ${caseName}`);
            } else {
                this.log('error', `Tool case missing: ${caseName}`);
                allRoutingFound = false;
            }
        }

        return allRoutingFound;
    }

    verifyPromptTemplates() {
        this.log('info', 'Verifying prompt templates...');

        const templatesFile = 'src/core/prompts/quantitative-templates.ts';
        if (!this.fileExists(templatesFile)) {
            this.log('error', `Templates file not found: ${templatesFile}`);
            return false;
        }

        const content = this.readFile(templatesFile);
        if (!content) return false;

        const requiredTemplates = [
            'INVESTMENT_IDEA_ANALYSIS_TEMPLATE',
            'STRATEGY_CODE_GENERATION_TEMPLATE',
            'BACKTEST_ANALYSIS_TEMPLATE',
            'RISK_METRICS_TEMPLATE',
            'PARAMETER_OPTIMIZATION_TEMPLATE'
        ];

        let allTemplatesFound = true;
        for (const template of requiredTemplates) {
            if (content.includes(template)) {
                this.log('success', `Template found: ${template}`);
            } else {
                this.log('error', `Template missing: ${template}`);
                allTemplatesFound = false;
            }
        }

        return allTemplatesFound;
    }

    verifyToolDescriptions() {
        this.log('info', 'Verifying tool descriptions...');

        const toolDescFiles = [
            'src/core/prompts/tools/generate-strategy.ts',
            'src/core/prompts/tools/backtest-strategy.ts',
            'src/core/prompts/tools/calculate-metrics.ts',
            'src/core/prompts/tools/optimize-parameters.ts'
        ];

        let allDescriptionsFound = true;
        for (const descFile of toolDescFiles) {
            if (this.fileExists(descFile)) {
                this.log('success', `Tool description found: ${path.basename(descFile)}`);
            } else {
                this.log('error', `Tool description missing: ${descFile}`);
                allDescriptionsFound = false;
            }
        }

        // Check index file
        const indexFile = 'src/core/prompts/tools/index.ts';
        if (this.fileExists(indexFile)) {
            const content = this.readFile(indexFile);
            if (content && content.includes('generate-strategy') && 
                content.includes('backtest-strategy') &&
                content.includes('calculate-metrics') &&
                content.includes('optimize-parameters')) {
                this.log('success', 'Tool descriptions properly indexed');
            } else {
                this.log('error', 'Tool descriptions not properly indexed');
                allDescriptionsFound = false;
            }
        } else {
            this.log('error', `Tool descriptions index file missing: ${indexFile}`);
            allDescriptionsFound = false;
        }

        return allDescriptionsFound;
    }

    async runVerification() {
        console.log('🔍 Starting Roo-Code Quantitative Mode Verification');
        console.log('=' .repeat(60));

        const verificationSteps = [
            { name: 'Mode Configuration', fn: () => this.verifyModeConfiguration() },
            { name: 'Tool Definitions', fn: () => this.verifyToolDefinitions() },
            { name: 'Tool Implementations', fn: () => this.verifyToolImplementations() },
            { name: 'Tool Routing', fn: () => this.verifyToolRouting() },
            { name: 'Prompt Templates', fn: () => this.verifyPromptTemplates() },
            { name: 'Tool Descriptions', fn: () => this.verifyToolDescriptions() }
        ];

        let passedSteps = 0;
        for (const step of verificationSteps) {
            console.log(`\n📋 Verifying: ${step.name}`);
            console.log('-'.repeat(40));
            
            if (step.fn()) {
                passedSteps++;
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log(`📊 Verification Results: ${passedSteps}/${verificationSteps.length} steps passed`);
        console.log(`✅ Successes: ${this.successes.length}`);
        console.log(`⚠️  Warnings: ${this.warnings.length}`);
        console.log(`❌ Errors: ${this.errors.length}`);

        if (passedSteps === verificationSteps.length && this.errors.length === 0) {
            console.log('\n🎉 Quantitative mode verification completed successfully!');
            console.log('✨ All components are properly integrated and ready for use.');
            return true;
        } else {
            console.log('\n⚠️  Verification completed with issues.');
            if (this.errors.length > 0) {
                console.log('\n❌ Critical errors that need to be fixed:');
                this.errors.forEach(error => console.log(`   • ${error}`));
            }
            if (this.warnings.length > 0) {
                console.log('\n⚠️  Warnings to consider:');
                this.warnings.forEach(warning => console.log(`   • ${warning}`));
            }
            return false;
        }
    }
}

// Run verification if this script is executed directly
if (require.main === module) {
    const verifier = new QuantModeVerifier();
    verifier.runVerification().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('Verification failed with error:', error);
        process.exit(1);
    });
}

module.exports = { QuantModeVerifier };
