# 量化模式测试文档

## 测试目标
验证新实现的量化模式是否能够正确工作，包括：
1. 模式切换功能
2. 量化工具的可用性
3. 投资理念到策略代码的转换
4. 回测分析功能
5. 性能指标计算
6. 参数优化功能

## 测试步骤

### 1. 切换到量化模式
使用 `switch_mode` 工具切换到量化模式：
```
<switch_mode>
<mode_slug>quant</mode_slug>
<reason>测试量化交易策略开发功能</reason>
</switch_mode>
```

### 2. 测试策略生成
使用投资理念生成策略代码：
```
<generate_strategy>
<investment_idea>科技股在财报季通常表现出强劲的动量效应，特别是在超预期财报发布后的2-3个月内会持续跑赢大盘</investment_idea>
<strategy_type>momentum</strategy_type>
<timeframe>daily</timeframe>
<risk_level>medium</risk_level>
</generate_strategy>
```

### 3. 测试回测功能
对生成的策略进行回测：
```
<backtest_strategy>
<strategy_code>[生成的策略代码]</strategy_code>
<start_date>2023-01-01</start_date>
<end_date>2024-01-01</end_date>
<initial_capital>100000</initial_capital>
<benchmark>QQQ</benchmark>
</backtest_strategy>
```

### 4. 测试性能指标计算
计算详细的性能指标：
```
<calculate_metrics>
<returns_data>[回测返回的收益数据]</returns_data>
<benchmark_data>[基准收益数据]</benchmark_data>
<risk_free_rate>0.02</risk_free_rate>
</calculate_metrics>
```

### 5. 测试参数优化
优化策略参数：
```
<optimize_parameters>
<strategy_code>[策略代码]</strategy_code>
<parameter_ranges>{"sma_short": {"min": 5, "max": 20, "step": 1}, "sma_long": {"min": 20, "max": 100, "step": 5}}</parameter_ranges>
<optimization_metric>sharpe_ratio</optimization_metric>
<constraints>max_drawdown < 0.15</constraints>
</optimize_parameters>
```

## 预期结果

### 策略生成工具应该返回：
- 完整的Python策略类
- 基于投资理念的技术指标和信号逻辑
- 风险管理和仓位管理规则
- 回测框架代码

### 回测工具应该返回：
- 详细的性能摘要表格
- 风险评估结果
- 财务摘要
- 策略洞察和建议

### 性能指标工具应该返回：
- 收益指标（总收益、年化收益、波动率）
- 风险调整指标（夏普比率、索提诺比率、卡尔马比率）
- 风险指标（最大回撤、VaR、CVaR）
- 分布指标（偏度、峰度）
- 基准相对指标（Alpha、Beta、信息比率）

### 参数优化工具应该返回：
- 最优参数组合
- 参数敏感性分析
- 优化收敛历史
- 优化洞察和建议

## 验证要点

1. **工具可用性**：确认量化模式下所有四个量化工具都可用
2. **参数验证**：确认必需参数缺失时会正确报错
3. **输出格式**：确认所有工具输出格式正确且包含预期信息
4. **错误处理**：确认异常情况下的错误处理机制
5. **用户体验**：确认工具使用流程顺畅，输出易于理解

## 成功标准

- [ ] 能够成功切换到量化模式
- [ ] 所有四个量化工具都能正常执行
- [ ] 策略生成工具能基于投资理念生成合理的策略代码
- [ ] 回测工具能提供详细的性能分析
- [ ] 性能指标工具能计算全面的风险收益指标
- [ ] 参数优化工具能找到最优参数组合
- [ ] 整个工作流程从理念到优化形成完整闭环

## 注意事项

1. 当前实现使用模拟数据进行演示
2. 实际生产环境需要集成真实的市场数据源
3. 策略代码模板可以根据具体需求进一步定制
4. 优化算法可以升级为更高级的方法（如遗传算法、贝叶斯优化等）
