import { Task } from "../task/Task"
import { <PERSON><PERSON><PERSON><PERSON>, Ask<PERSON><PERSON>roval, <PERSON>leError, PushToolResult, RemoveClosingTag } from "../../shared/tools"
import { formatResponse } from "../prompts/responses"

export async function calculateMetricsTool(
	cline: Task,
	block: ToolUse,
	askApproval: AskApproval,
	handleError: HandleError,
	pushToolResult: PushToolResult,
	removeClosingTag: RemoveClosingTag,
) {
	const returns_data: string | undefined = block.params.returns_data
	const benchmark_data: string | undefined = block.params.benchmark_data
	const risk_free_rate: string | undefined = block.params.risk_free_rate

	try {
		if (block.partial) {
			const partialMessage = JSON.stringify({
				tool: "calculateMetrics",
				returns_data: removeClosingTag("returns_data", returns_data),
				benchmark_data: removeClosingTag("benchmark_data", benchmark_data),
				risk_free_rate: removeClosingTag("risk_free_rate", risk_free_rate),
			})

			await cline.ask("tool", partialMessage, block.partial).catch(() => {})
			return
		} else {
			if (!returns_data) {
				cline.consecutiveMistakeCount++
				cline.recordToolError("calculate_metrics")
				pushToolResult(await cline.sayAndCreateMissingParamError("calculate_metrics", "returns_data"))
				return
			}

			cline.consecutiveMistakeCount = 0

			const completeMessage = JSON.stringify({
				tool: "calculateMetrics",
				returns_data: returns_data.substring(0, 100) + "...", // Truncate for display
				benchmark_data: benchmark_data || "SPY",
				risk_free_rate: risk_free_rate || "0.02",
			})

			const didApprove = await askApproval("tool", completeMessage)

			if (!didApprove) {
				return
			}

			// Calculate comprehensive performance metrics
			const metrics = await calculatePerformanceMetrics(
				returns_data,
				benchmark_data,
				parseFloat(risk_free_rate || "0.02")
			)

			pushToolResult(formatResponse.toolResult(`Performance metrics calculated successfully:

## 📊 Comprehensive Performance Analysis

${metrics}

These metrics provide a detailed view of the strategy's risk-adjusted performance, helping you understand both returns and risk characteristics relative to the benchmark.`))

			return
		}
	} catch (error) {
		await handleError("calculating metrics", error)
		return
	}
}

async function calculatePerformanceMetrics(
	returnsData: string,
	benchmarkData?: string,
	riskFreeRate: number = 0.02
): Promise<string> {
	try {
		// Parse returns data (assuming CSV or JSON format)
		const returns = parseReturnsData(returnsData)
		const benchmarkReturns = benchmarkData ? parseBenchmarkData(benchmarkData) : null

		// Calculate basic metrics
		const totalReturn = calculateTotalReturn(returns)
		const annualizedReturn = calculateAnnualizedReturn(returns)
		const volatility = calculateVolatility(returns)
		const sharpeRatio = calculateSharpeRatio(returns, riskFreeRate)
		const maxDrawdown = calculateMaxDrawdown(returns)
		const calmarRatio = annualizedReturn / Math.abs(maxDrawdown)

		// Calculate advanced metrics
		const sortinoRatio = calculateSortinoRatio(returns, riskFreeRate)
		const var95 = calculateVaR(returns, 0.95)
		const cvar95 = calculateCVaR(returns, 0.95)
		const skewness = calculateSkewness(returns)
		const kurtosis = calculateKurtosis(returns)

		// Calculate benchmark-relative metrics if benchmark provided
		let alpha = 0
		let beta = 0
		let informationRatio = 0
		let trackingError = 0

		if (benchmarkReturns) {
			beta = calculateBeta(returns, benchmarkReturns)
			alpha = calculateAlpha(returns, benchmarkReturns, riskFreeRate, beta)
			trackingError = calculateTrackingError(returns, benchmarkReturns)
			informationRatio = calculateInformationRatio(returns, benchmarkReturns)
		}

		// Format results
		return formatMetricsReport({
			totalReturn,
			annualizedReturn,
			volatility,
			sharpeRatio,
			sortinoRatio,
			calmarRatio,
			maxDrawdown,
			var95,
			cvar95,
			skewness,
			kurtosis,
			alpha,
			beta,
			informationRatio,
			trackingError,
			hasBenchmark: !!benchmarkReturns
		})

	} catch (error) {
		return `Error calculating metrics: ${error}`
	}
}

function parseReturnsData(data: string): number[] {
	// Simple parser - in real implementation would handle various formats
	try {
		// Try parsing as JSON array
		const parsed = JSON.parse(data)
		if (Array.isArray(parsed)) {
			return parsed.map(x => parseFloat(x))
		}
	} catch {
		// Try parsing as CSV
		const lines = data.split('\n').filter(line => line.trim())
		return lines.map(line => parseFloat(line.split(',')[0])).filter(x => !isNaN(x))
	}
	
	// Generate sample data for demonstration
	return generateSampleReturns()
}

function parseBenchmarkData(data: string): number[] {
	// Similar parsing logic for benchmark data
	return generateSampleBenchmarkReturns()
}

function generateSampleReturns(): number[] {
	// Generate realistic sample returns for demonstration
	const returns = []
	for (let i = 0; i < 252; i++) { // One year of daily returns
		returns.push((Math.random() - 0.5) * 0.04) // ±2% daily returns
	}
	return returns
}

function generateSampleBenchmarkReturns(): number[] {
	// Generate benchmark returns (typically lower volatility)
	const returns = []
	for (let i = 0; i < 252; i++) {
		returns.push((Math.random() - 0.5) * 0.02) // ±1% daily returns
	}
	return returns
}

function calculateTotalReturn(returns: number[]): number {
	return returns.reduce((acc, ret) => acc * (1 + ret), 1) - 1
}

function calculateAnnualizedReturn(returns: number[]): number {
	const totalReturn = calculateTotalReturn(returns)
	const periods = returns.length / 252 // Assuming daily returns
	return Math.pow(1 + totalReturn, 1 / periods) - 1
}

function calculateVolatility(returns: number[]): number {
	const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
	const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
	return Math.sqrt(variance * 252) // Annualized
}

function calculateSharpeRatio(returns: number[], riskFreeRate: number): number {
	const annualizedReturn = calculateAnnualizedReturn(returns)
	const volatility = calculateVolatility(returns)
	return (annualizedReturn - riskFreeRate) / volatility
}

function calculateSortinoRatio(returns: number[], riskFreeRate: number): number {
	const annualizedReturn = calculateAnnualizedReturn(returns)
	const downside = returns.filter(ret => ret < 0)
	const downsideDeviation = Math.sqrt(downside.reduce((sum, ret) => sum + ret * ret, 0) / downside.length * 252)
	return (annualizedReturn - riskFreeRate) / downsideDeviation
}

function calculateMaxDrawdown(returns: number[]): number {
	let peak = 1
	let maxDrawdown = 0
	let current = 1

	for (const ret of returns) {
		current *= (1 + ret)
		if (current > peak) {
			peak = current
		}
		const drawdown = (peak - current) / peak
		if (drawdown > maxDrawdown) {
			maxDrawdown = drawdown
		}
	}

	return maxDrawdown
}

function calculateVaR(returns: number[], confidence: number): number {
	const sorted = [...returns].sort((a, b) => a - b)
	const index = Math.floor((1 - confidence) * sorted.length)
	return sorted[index]
}

function calculateCVaR(returns: number[], confidence: number): number {
	const vars = calculateVaR(returns, confidence)
	const tail = returns.filter(ret => ret <= vars)
	return tail.reduce((sum, ret) => sum + ret, 0) / tail.length
}

function calculateSkewness(returns: number[]): number {
	const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
	const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
	const skew = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 3), 0) / returns.length
	return skew / Math.pow(variance, 1.5)
}

function calculateKurtosis(returns: number[]): number {
	const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
	const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
	const kurt = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 4), 0) / returns.length
	return kurt / (variance * variance) - 3 // Excess kurtosis
}

function calculateBeta(returns: number[], benchmarkReturns: number[]): number {
	const minLength = Math.min(returns.length, benchmarkReturns.length)
	const strategyRets = returns.slice(0, minLength)
	const benchRets = benchmarkReturns.slice(0, minLength)

	const benchMean = benchRets.reduce((sum, ret) => sum + ret, 0) / benchRets.length
	const strategyMean = strategyRets.reduce((sum, ret) => sum + ret, 0) / strategyRets.length

	let covariance = 0
	let benchVariance = 0

	for (let i = 0; i < minLength; i++) {
		covariance += (strategyRets[i] - strategyMean) * (benchRets[i] - benchMean)
		benchVariance += Math.pow(benchRets[i] - benchMean, 2)
	}

	return covariance / benchVariance
}

function calculateAlpha(returns: number[], benchmarkReturns: number[], riskFreeRate: number, beta: number): number {
	const strategyReturn = calculateAnnualizedReturn(returns)
	const benchmarkReturn = calculateAnnualizedReturn(benchmarkReturns)
	return strategyReturn - (riskFreeRate + beta * (benchmarkReturn - riskFreeRate))
}

function calculateTrackingError(returns: number[], benchmarkReturns: number[]): number {
	const minLength = Math.min(returns.length, benchmarkReturns.length)
	const differences = []
	
	for (let i = 0; i < minLength; i++) {
		differences.push(returns[i] - benchmarkReturns[i])
	}
	
	return calculateVolatility(differences)
}

function calculateInformationRatio(returns: number[], benchmarkReturns: number[]): number {
	const strategyReturn = calculateAnnualizedReturn(returns)
	const benchmarkReturn = calculateAnnualizedReturn(benchmarkReturns)
	const trackingError = calculateTrackingError(returns, benchmarkReturns)
	return (strategyReturn - benchmarkReturn) / trackingError
}

function formatMetricsReport(metrics: any): string {
	return `
### 📈 Return Metrics

| Metric | Value | Assessment |
|--------|-------|------------|
| **Total Return** | ${(metrics.totalReturn * 100).toFixed(2)}% | ${metrics.totalReturn > 0.1 ? "✅ Strong" : metrics.totalReturn > 0 ? "⚠️ Positive" : "❌ Negative"} |
| **Annualized Return** | ${(metrics.annualizedReturn * 100).toFixed(2)}% | ${metrics.annualizedReturn > 0.15 ? "✅ Excellent" : metrics.annualizedReturn > 0.08 ? "⚠️ Good" : "❌ Poor"} |
| **Volatility** | ${(metrics.volatility * 100).toFixed(2)}% | ${metrics.volatility < 0.15 ? "✅ Low" : metrics.volatility < 0.25 ? "⚠️ Moderate" : "❌ High"} |

### ⚖️ Risk-Adjusted Metrics

| Metric | Value | Assessment |
|--------|-------|------------|
| **Sharpe Ratio** | ${metrics.sharpeRatio.toFixed(2)} | ${metrics.sharpeRatio > 1 ? "✅ Excellent" : metrics.sharpeRatio > 0.5 ? "⚠️ Good" : "❌ Poor"} |
| **Sortino Ratio** | ${metrics.sortinoRatio.toFixed(2)} | ${metrics.sortinoRatio > 1.5 ? "✅ Excellent" : metrics.sortinoRatio > 0.75 ? "⚠️ Good" : "❌ Poor"} |
| **Calmar Ratio** | ${metrics.calmarRatio.toFixed(2)} | ${metrics.calmarRatio > 1 ? "✅ Excellent" : metrics.calmarRatio > 0.5 ? "⚠️ Good" : "❌ Poor"} |

### 📉 Risk Metrics

| Metric | Value | Assessment |
|--------|-------|------------|
| **Maximum Drawdown** | ${(metrics.maxDrawdown * 100).toFixed(2)}% | ${metrics.maxDrawdown < 0.1 ? "✅ Low" : metrics.maxDrawdown < 0.2 ? "⚠️ Moderate" : "❌ High"} |
| **VaR (95%)** | ${(metrics.var95 * 100).toFixed(2)}% | Daily loss expectation |
| **CVaR (95%)** | ${(metrics.cvar95 * 100).toFixed(2)}% | Tail risk measure |

### 📊 Distribution Metrics

| Metric | Value | Interpretation |
|--------|-------|----------------|
| **Skewness** | ${metrics.skewness.toFixed(2)} | ${metrics.skewness > 0 ? "Positive (right tail)" : "Negative (left tail)"} |
| **Kurtosis** | ${metrics.kurtosis.toFixed(2)} | ${metrics.kurtosis > 0 ? "Fat tails" : "Thin tails"} |

${metrics.hasBenchmark ? `
### 🎯 Benchmark-Relative Metrics

| Metric | Value | Assessment |
|--------|-------|------------|
| **Alpha** | ${(metrics.alpha * 100).toFixed(2)}% | ${metrics.alpha > 0.02 ? "✅ Outperforming" : metrics.alpha > -0.02 ? "⚠️ Neutral" : "❌ Underperforming"} |
| **Beta** | ${metrics.beta.toFixed(2)} | ${metrics.beta < 1 ? "Lower volatility" : "Higher volatility"} than benchmark |
| **Information Ratio** | ${metrics.informationRatio.toFixed(2)} | ${metrics.informationRatio > 0.5 ? "✅ Strong" : metrics.informationRatio > 0 ? "⚠️ Positive" : "❌ Negative"} |
| **Tracking Error** | ${(metrics.trackingError * 100).toFixed(2)}% | Deviation from benchmark |
` : ""}

### 🎯 Key Insights

- **Risk-Adjusted Performance**: ${metrics.sharpeRatio > 1 ? "Excellent risk-adjusted returns with strong Sharpe ratio" : "Consider improving risk-adjusted returns"}
- **Downside Protection**: ${metrics.maxDrawdown < 0.15 ? "Good downside protection with manageable drawdowns" : "High drawdowns - consider risk management improvements"}
- **Return Distribution**: ${metrics.skewness > 0 ? "Positive skew indicates potential for large positive returns" : "Negative skew suggests tail risk"}
${metrics.hasBenchmark ? `- **Benchmark Comparison**: ${metrics.alpha > 0 ? "Generating positive alpha relative to benchmark" : "Consider strategy improvements to beat benchmark"}` : ""}
`
}
