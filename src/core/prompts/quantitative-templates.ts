/**
 * Quantitative Trading Strategy Templates and Prompts
 * 
 * This module provides specialized templates for understanding investment ideas
 * and generating structured quantitative trading strategies.
 */

export interface InvestmentIdeaAnalysis {
	concept: string
	marketHypothesis: string
	timeHorizon: string
	riskFactors: string[]
	expectedSignals: string[]
	implementationApproach: string
}

export interface StrategyStructure {
	entryConditions: string[]
	exitConditions: string[]
	riskManagement: string[]
	positionSizing: string
	technicalIndicators: string[]
	fundamentalFactors?: string[]
}

/**
 * Template for analyzing and structuring investment ideas
 */
export const INVESTMENT_IDEA_ANALYSIS_TEMPLATE = `
Analyze the following investment idea and extract key components:

Investment Idea: {investment_idea}

Please provide a structured analysis covering:

1. **Core Investment Concept**
   - What is the fundamental market inefficiency or pattern being exploited?
   - What is the theoretical basis for this strategy?

2. **Market Hypothesis**
   - What market conditions does this strategy assume?
   - What behavioral or structural factors drive the expected returns?

3. **Time Horizon**
   - What is the expected holding period for positions?
   - How frequently should the strategy rebalance?

4. **Risk Factors**
   - What are the primary risks that could cause the strategy to fail?
   - What market conditions would be adverse to this approach?

5. **Expected Signals**
   - What specific market signals or indicators should trigger trades?
   - How can these signals be quantified and measured?

6. **Implementation Approach**
   - What data sources and technical indicators are needed?
   - What is the recommended portfolio construction method?

Format your response as a structured analysis that can be translated into algorithmic trading rules.
`

/**
 * Template for generating strategy code structure
 */
export const STRATEGY_CODE_GENERATION_TEMPLATE = `
Based on the investment analysis, generate a quantitative trading strategy with the following structure:

Investment Analysis: {analysis}
Strategy Type: {strategy_type}
Timeframe: {timeframe}
Risk Level: {risk_level}

Generate Python code that implements:

1. **Data Management**
   - Historical data fetching and preprocessing
   - Real-time data handling capabilities
   - Data validation and cleaning procedures

2. **Signal Generation**
   - Technical indicator calculations
   - Signal combination and filtering logic
   - Signal strength and confidence scoring

3. **Risk Management**
   - Position sizing based on volatility and risk budget
   - Stop-loss and take-profit mechanisms
   - Portfolio-level risk controls

4. **Execution Logic**
   - Entry and exit timing rules
   - Order management and execution
   - Transaction cost considerations

5. **Performance Tracking**
   - Return calculation and attribution
   - Risk metric monitoring
   - Strategy performance diagnostics

The code should be production-ready with proper error handling, logging, and documentation.
Include comprehensive comments explaining the investment logic behind each component.
`

/**
 * Template for backtesting analysis
 */
export const BACKTEST_ANALYSIS_TEMPLATE = `
Conduct a comprehensive backtesting analysis for the quantitative strategy:

Strategy Code: {strategy_code}
Backtest Period: {start_date} to {end_date}
Initial Capital: {initial_capital}
Benchmark: {benchmark}

Provide detailed analysis covering:

1. **Performance Summary**
   - Total and annualized returns
   - Risk-adjusted performance metrics
   - Comparison to benchmark performance

2. **Risk Analysis**
   - Maximum drawdown and recovery periods
   - Volatility analysis and risk decomposition
   - Value-at-Risk and stress testing results

3. **Trade Analysis**
   - Win/loss ratios and trade distribution
   - Average holding periods and turnover
   - Transaction cost impact assessment

4. **Market Regime Analysis**
   - Performance across different market conditions
   - Correlation with market factors
   - Strategy robustness evaluation

5. **Implementation Considerations**
   - Liquidity requirements and market impact
   - Scalability and capacity constraints
   - Operational risk factors

Generate actionable insights and recommendations for strategy improvement.
`

/**
 * Template for risk metrics calculation
 */
export const RISK_METRICS_TEMPLATE = `
Calculate comprehensive risk and performance metrics for the trading strategy:

Returns Data: {returns_data}
Benchmark Data: {benchmark_data}
Risk-Free Rate: {risk_free_rate}

Compute and analyze:

1. **Return Metrics**
   - Arithmetic and geometric mean returns
   - Compound annual growth rate (CAGR)
   - Return distribution characteristics

2. **Risk Metrics**
   - Standard deviation and downside deviation
   - Maximum drawdown and average drawdown
   - Value-at-Risk (VaR) at multiple confidence levels
   - Conditional Value-at-Risk (CVaR)

3. **Risk-Adjusted Metrics**
   - Sharpe ratio and modified Sharpe ratio
   - Sortino ratio and Calmar ratio
   - Information ratio and Treynor ratio
   - Omega ratio and gain-to-pain ratio

4. **Benchmark-Relative Metrics**
   - Alpha and beta coefficients
   - Tracking error and information ratio
   - Up/down capture ratios
   - Correlation and R-squared

5. **Distribution Analysis**
   - Skewness and kurtosis
   - Jarque-Bera normality test
   - Tail risk assessment

Provide interpretation and context for each metric relative to industry benchmarks.
`

/**
 * Template for parameter optimization
 */
export const PARAMETER_OPTIMIZATION_TEMPLATE = `
Optimize strategy parameters to maximize risk-adjusted performance:

Strategy Code: {strategy_code}
Parameter Ranges: {parameter_ranges}
Optimization Metric: {optimization_metric}
Constraints: {constraints}

Execute systematic optimization process:

1. **Parameter Space Definition**
   - Define feasible ranges for each parameter
   - Identify parameter interactions and dependencies
   - Set optimization constraints and boundaries

2. **Optimization Algorithm**
   - Grid search or random search methodology
   - Cross-validation and out-of-sample testing
   - Overfitting prevention techniques

3. **Performance Evaluation**
   - Multi-objective optimization considerations
   - Robustness testing across market regimes
   - Sensitivity analysis for key parameters

4. **Validation Framework**
   - Walk-forward analysis
   - Monte Carlo simulation
   - Bootstrap confidence intervals

5. **Implementation Guidelines**
   - Parameter stability assessment
   - Reoptimization frequency recommendations
   - Live trading adaptation procedures

Provide optimal parameter values with confidence intervals and implementation guidance.
`

/**
 * Common investment idea patterns and their implementations
 */
export const INVESTMENT_PATTERNS = {
	momentum: {
		description: "Trend-following strategies that capitalize on price momentum",
		indicators: ["Moving averages", "RSI", "MACD", "Price rate of change"],
		signals: ["Price above moving average", "RSI trending up", "Volume confirmation"],
		riskFactors: ["Trend reversals", "Whipsaws", "Market regime changes"]
	},
	
	meanReversion: {
		description: "Strategies that profit from price reversions to historical means",
		indicators: ["Bollinger Bands", "Z-score", "RSI", "Stochastic"],
		signals: ["Price deviation from mean", "Oversold/overbought conditions"],
		riskFactors: ["Trending markets", "Structural breaks", "Volatility regimes"]
	},
	
	arbitrage: {
		description: "Risk-free profit from price discrepancies across markets",
		indicators: ["Price spreads", "Correlation measures", "Cointegration"],
		signals: ["Spread divergence", "Statistical significance", "Execution timing"],
		riskFactors: ["Execution risk", "Model breakdown", "Liquidity constraints"]
	},
	
	factorBased: {
		description: "Systematic exposure to risk factors that generate returns",
		indicators: ["Factor loadings", "Factor momentum", "Factor valuations"],
		signals: ["Factor strength", "Factor rotation", "Risk-adjusted exposure"],
		riskFactors: ["Factor crowding", "Regime changes", "Factor decay"]
	}
}

/**
 * Risk management templates for different strategy types
 */
export const RISK_MANAGEMENT_TEMPLATES = {
	volatilityTargeting: `
	# Volatility Targeting Risk Management
	target_volatility = {target_vol}
	lookback_period = {lookback}
	
	def calculate_position_size(returns, target_vol):
		realized_vol = returns.rolling(lookback_period).std() * np.sqrt(252)
		vol_scalar = target_vol / realized_vol
		return np.clip(vol_scalar, 0.1, 3.0)  # Limit leverage
	`,
	
	kellyOptimal: `
	# Kelly Criterion Position Sizing
	def kelly_position_size(win_rate, avg_win, avg_loss):
		if avg_loss == 0:
			return 0
		kelly_fraction = win_rate - ((1 - win_rate) * avg_win / avg_loss)
		return max(0, min(kelly_fraction * 0.25, 0.1))  # Conservative Kelly
	`,
	
	riskParity: `
	# Risk Parity Portfolio Construction
	def risk_parity_weights(cov_matrix):
		n_assets = len(cov_matrix)
		weights = np.ones(n_assets) / n_assets
		
		for _ in range(100):  # Iterative optimization
			portfolio_vol = np.sqrt(weights @ cov_matrix @ weights)
			marginal_contrib = cov_matrix @ weights / portfolio_vol
			weights = weights * (1 / marginal_contrib)
			weights = weights / weights.sum()
		
		return weights
	`
}

/**
 * Generate strategy template based on investment idea
 */
export function generateStrategyTemplate(
	investmentIdea: string,
	strategyType: string,
	timeframe: string,
	riskLevel: string
): string {
	const pattern = INVESTMENT_PATTERNS[strategyType as keyof typeof INVESTMENT_PATTERNS]
	
	return `
# Quantitative Strategy: ${investmentIdea}

## Strategy Overview
- **Type**: ${strategyType}
- **Timeframe**: ${timeframe}
- **Risk Level**: ${riskLevel}
- **Investment Thesis**: ${investmentIdea}

## Implementation Framework
${pattern ? `
### Key Indicators
${pattern.indicators.map(ind => `- ${ind}`).join('\n')}

### Signal Generation
${pattern.signals.map(sig => `- ${sig}`).join('\n')}

### Risk Factors
${pattern.riskFactors.map(risk => `- ${risk}`).join('\n')}
` : ''}

## Code Structure
1. Data Management and Preprocessing
2. Technical Indicator Calculations
3. Signal Generation and Filtering
4. Risk Management and Position Sizing
5. Portfolio Construction and Rebalancing
6. Performance Monitoring and Reporting

This template provides the foundation for implementing the quantitative strategy.
Customize the specific parameters and logic based on the investment hypothesis.
`
}
