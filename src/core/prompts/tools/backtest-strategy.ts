export function getBacktestStrategyDescription(): string {
	return `## backtest_strategy
Description: Execute a comprehensive backtest of a quantitative trading strategy to evaluate its historical performance. This tool runs the strategy against historical market data, calculates performance metrics, and provides detailed analysis of returns, risk, and trading behavior.
Parameters:
- strategy_code: (required) The complete strategy code to backtest (Python class or function)
- start_date: (optional) Backtest start date in YYYY-MM-DD format (default: "2023-01-01")
- end_date: (optional) Backtest end date in YYYY-MM-DD format (default: "2024-01-01")
- initial_capital: (optional) Starting capital amount in USD (default: "100000")
- benchmark: (optional) Benchmark symbol for comparison (default: "SPY")

Usage:
<backtest_strategy>
<strategy_code>Complete strategy code here</strategy_code>
<start_date>2023-01-01</start_date>
<end_date>2024-01-01</end_date>
<initial_capital>100000</initial_capital>
<benchmark>SPY</benchmark>
</backtest_strategy>

Example: Backtest a momentum strategy
<backtest_strategy>
<strategy_code>
class MomentumStrategy:
    def __init__(self, symbols, initial_capital=100000):
        # Strategy implementation here
        pass
    
    def backtest(self, start_date, end_date):
        # Backtesting logic here
        pass
</strategy_code>
<start_date>2022-01-01</start_date>
<end_date>2023-12-31</end_date>
<initial_capital>250000</initial_capital>
<benchmark>QQQ</benchmark>
</backtest_strategy>

The tool will provide:
1. **Performance Summary**: Total return, annualized return, volatility, Sharpe ratio
2. **Risk Metrics**: Maximum drawdown, VaR, win rate, total trades
3. **Financial Summary**: Final portfolio value, profit/loss breakdown
4. **Risk Assessment**: Automated evaluation of risk-adjusted returns
5. **Strategy Insights**: Trade analysis and performance patterns
6. **Benchmark Comparison**: Performance relative to specified benchmark
7. **Next Steps**: Recommendations for optimization and improvement

The backtest includes realistic assumptions about transaction costs, slippage, and market impact to provide accurate performance estimates suitable for strategy evaluation and decision-making.`
}
