export function getGenerateStrategyDescription(): string {
	return `## generate_strategy
Description: Generate a quantitative trading strategy based on an investment idea or concept. This tool translates investment theories, market insights, or trading concepts into structured, executable strategy code with proper risk management, entry/exit logic, and backtesting capabilities.
Parameters:
- investment_idea: (required) The core investment concept, market insight, or trading theory to implement (e.g., "momentum in tech stocks", "mean reversion in commodities", "pairs trading in energy sector")
- strategy_type: (optional) The type of strategy framework to use - "momentum", "mean_reversion", "arbitrage", "pairs_trading", "statistical_arbitrage", or "custom" (default: "momentum")
- timeframe: (optional) The trading timeframe - "intraday", "daily", "weekly", "monthly" (default: "daily")
- risk_level: (optional) Risk tolerance level - "low", "medium", "high" (default: "medium")

Usage:
<generate_strategy>
<investment_idea>Investment concept or market insight here</investment_idea>
<strategy_type>momentum</strategy_type>
<timeframe>daily</timeframe>
<risk_level>medium</risk_level>
</generate_strategy>

Example: Generate a momentum strategy for tech stocks
<generate_strategy>
<investment_idea>Technology stocks tend to exhibit strong momentum during earnings seasons, with winners continuing to outperform for 2-3 months after positive earnings surprises</investment_idea>
<strategy_type>momentum</strategy_type>
<timeframe>daily</timeframe>
<risk_level>medium</risk_level>
</generate_strategy>

The tool will generate:
1. Complete Python strategy code with proper class structure
2. Technical indicators and signal generation logic
3. Risk management and position sizing rules
4. Entry and exit conditions based on the investment idea
5. Backtesting framework ready for immediate testing
6. Performance tracking and monitoring capabilities

The generated strategy will be production-ready with error handling, proper data validation, and comprehensive documentation explaining the implementation of the investment concept.`
}
