export function getOptimizeParametersDescription(): string {
	return `## optimize_parameters
Description: Optimize trading strategy parameters to maximize performance using systematic parameter search and sensitivity analysis. This tool finds the optimal combination of strategy parameters by testing multiple configurations and identifying the best-performing settings based on specified optimization criteria.
Parameters:
- strategy_code: (required) The complete strategy code containing parameters to optimize
- parameter_ranges: (optional) JSON object or simple format defining parameter search ranges (default: common strategy parameters)
- optimization_metric: (optional) Target metric to optimize - "sharpe_ratio", "total_return", "calmar_ratio", "sortino_ratio" (default: "sharpe_ratio")
- constraints: (optional) Optimization constraints or restrictions (default: "None")

Usage:
<optimize_parameters>
<strategy_code>Complete strategy code with parameters to optimize</strategy_code>
<parameter_ranges>{"sma_short": {"min": 5, "max": 20, "step": 1}, "sma_long": {"min": 20, "max": 100, "step": 5}}</parameter_ranges>
<optimization_metric>sharpe_ratio</optimization_metric>
<constraints>max_drawdown < 0.15</constraints>
</optimize_parameters>

Example: Optimize a momentum strategy parameters
<optimize_parameters>
<strategy_code>
class MomentumStrategy:
    def __init__(self, sma_short=10, sma_long=50, rsi_period=14, risk_per_trade=0.02):
        self.sma_short = sma_short
        self.sma_long = sma_long
        self.rsi_period = rsi_period
        self.risk_per_trade = risk_per_trade
    # ... rest of strategy code
</strategy_code>
<parameter_ranges>
{
  "sma_short": {"min": 5, "max": 25, "step": 2},
  "sma_long": {"min": 30, "max": 100, "step": 10},
  "rsi_period": {"min": 10, "max": 30, "step": 2},
  "risk_per_trade": {"min": 0.01, "max": 0.05, "step": 0.005}
}
</parameter_ranges>
<optimization_metric>sharpe_ratio</optimization_metric>
<constraints>max_drawdown < 0.20, min_trades > 50</constraints>
</optimize_parameters>

Alternative simple format for parameter ranges:
<parameter_ranges>sma_short:5-25, sma_long:30-100, rsi_period:10-30, risk_per_trade:0.01-0.05</parameter_ranges>

The tool provides:

### Best Parameters Found
- Optimal parameter values with impact assessment
- Performance improvement quantification
- Parameter stability analysis

### Optimization Performance  
- Best achieved metric value
- Number of iterations completed
- Convergence analysis

### Parameter Sensitivity Analysis
- Correlation between each parameter and target metric
- Parameter importance ranking (High/Medium/Low impact)
- Direction of parameter influence (positive/negative)

### Convergence History
- Performance progression during optimization
- Last 10 iterations showing parameter evolution
- Optimization stability assessment

### Optimization Insights
1. **Most Important Parameters**: Top 3 parameters driving performance
2. **Parameter Stability**: Assessment of optimization convergence
3. **Further Optimization**: Recommendations for additional improvements

### Next Steps
1. **Implementation**: How to apply optimized parameters
2. **Validation**: Out-of-sample testing recommendations  
3. **Monitoring**: Live performance tracking guidance
4. **Re-optimization**: Schedule for parameter updates

### Important Warnings
- Over-optimization risks and mitigation strategies
- Out-of-sample validation requirements
- Transaction cost and slippage considerations
- Parameter drift monitoring in live trading

The optimization uses systematic grid search with statistical validation to ensure robust parameter selection while avoiding over-fitting to historical data.`
}
