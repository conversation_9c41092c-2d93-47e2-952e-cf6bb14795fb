export function getCalculateMetricsDescription(): string {
	return `## calculate_metrics
Description: Calculate comprehensive performance and risk metrics for trading strategy returns. This tool provides detailed statistical analysis including risk-adjusted returns, drawdown analysis, distribution metrics, and benchmark-relative performance measures essential for quantitative strategy evaluation.
Parameters:
- returns_data: (required) Strategy returns data in JSON array format or CSV format (daily/periodic returns)
- benchmark_data: (optional) Benchmark returns data for relative performance analysis (default: uses SPY data)
- risk_free_rate: (optional) Annual risk-free rate for Sharpe ratio calculation (default: "0.02" for 2%)

Usage:
<calculate_metrics>
<returns_data>[0.01, -0.005, 0.02, 0.015, -0.01, ...]</returns_data>
<benchmark_data>[0.008, -0.003, 0.012, 0.01, -0.008, ...]</benchmark_data>
<risk_free_rate>0.025</risk_free_rate>
</calculate_metrics>

Example: Calculate metrics for a momentum strategy
<calculate_metrics>
<returns_data>
[0.012, -0.008, 0.025, 0.018, -0.015, 0.009, 0.031, -0.012, 0.007, 0.022, -0.019, 0.014, 0.028, -0.006, 0.011, 0.035, -0.021, 0.016, 0.004, -0.013]
</returns_data>
<benchmark_data>
[0.008, -0.005, 0.015, 0.012, -0.010, 0.006, 0.018, -0.008, 0.005, 0.014, -0.012, 0.009, 0.016, -0.004, 0.007, 0.020, -0.015, 0.011, 0.003, -0.009]
</benchmark_data>
<risk_free_rate>0.03</risk_free_rate>
</calculate_metrics>

The tool calculates and provides:

### Return Metrics
- Total Return, Annualized Return, Volatility with performance assessments

### Risk-Adjusted Metrics  
- Sharpe Ratio: Risk-adjusted return measure
- Sortino Ratio: Downside risk-adjusted return
- Calmar Ratio: Return to maximum drawdown ratio

### Risk Metrics
- Maximum Drawdown: Largest peak-to-trough decline
- Value at Risk (VaR): Expected loss at 95% confidence
- Conditional VaR (CVaR): Expected loss in worst 5% scenarios

### Distribution Analysis
- Skewness: Return distribution asymmetry
- Kurtosis: Tail thickness and extreme event probability

### Benchmark-Relative Metrics (when benchmark provided)
- Alpha: Excess return over benchmark (risk-adjusted)
- Beta: Sensitivity to benchmark movements
- Information Ratio: Active return per unit of tracking error
- Tracking Error: Standard deviation of active returns

### Key Insights
- Automated performance assessment with color-coded ratings
- Risk profile evaluation and recommendations
- Benchmark comparison analysis
- Strategy improvement suggestions

All metrics include interpretive guidance and industry-standard benchmarks for easy evaluation of strategy performance and risk characteristics.`
}
