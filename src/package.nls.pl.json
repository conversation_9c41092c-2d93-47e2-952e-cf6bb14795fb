{"extension.displayName": "Roo Code (wcześniej Roo Cline)", "extension.description": "Pełny zespół programistów AI w twoim edytorze.", "command.newTask.title": "Nowe Zadanie", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "Ulepsz Kod", "command.addToContext.title": "Dodaj do Kontekstu", "command.openInNewTab.title": "Otwórz w Nowej Karcie", "command.focusInput.title": "Fokus na Pole Wprowadzania", "command.setCustomStoragePath.title": "Ustaw Niestandardową Ścieżkę Przechowywania", "command.terminal.addToContext.title": "Dodaj <PERSON>ć Terminala do Kontekstu", "command.terminal.fixCommand.title": "Napraw tę <PERSON>", "command.terminal.explainCommand.title": "Wyjaśnij tę Komendę", "command.acceptInput.title": "Akceptuj Wprowadzanie/Sugestię", "views.activitybar.title": "Roo Code", "views.contextMenu.label": "Roo Code", "views.terminalMenu.label": "Roo Code", "views.sidebar.name": "Roo Code", "command.mcpServers.title": "Serwery MCP", "command.prompts.title": "Tryby", "command.history.title": "Historia", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Otwórz w Edytorze", "command.settings.title": "Ustawienia", "command.documentation.title": "Dokumentacja", "configuration.title": "Roo Code", "commands.allowedCommands.description": "Polecenia, które mogą być wykonywane automatycznie, gdy włączona jest opcja '<PERSON><PERSON><PERSON> zatwierdzaj operacje wykonania'", "settings.vsCodeLmModelSelector.description": "Ustawienia dla API modelu językowego VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Dostawca modelu językowego (np. copilot)", "settings.vsCodeLmModelSelector.family.description": "Rodzina modelu językowego (np. gpt-4)", "settings.customStoragePath.description": "Niestandardowa ścieżka przechowywania. Pozostaw puste, aby użyć domyślnej lokalizacji. Obsługuje ścieżki bezwzględne (np. 'D:\\RooCodeStorage')", "settings.rooCodeCloudEnabled.description": "Włącz Roo Code Cloud."}