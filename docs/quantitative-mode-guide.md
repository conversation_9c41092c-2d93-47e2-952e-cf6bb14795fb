# Roo-Code 量化交易模式使用指南

## 概述

Roo-Code 的量化交易模式是一个专门为量化策略开发设计的AI助手模式。它能够：

- 📈 **理解投资理念** - 将自然语言描述的投资想法转换为结构化的量化策略
- 🔧 **生成策略代码** - 自动生成完整的Python量化交易策略代码
- 📊 **回测分析** - 对策略进行历史数据回测，评估性能表现
- 📉 **风险评估** - 计算详细的风险指标和性能指标
- ⚙️ **参数优化** - 系统性地优化策略参数以提升表现

## 快速开始

### 1. 切换到量化模式

首先，使用 `switch_mode` 工具切换到量化模式：

```xml
<switch_mode>
<mode_slug>quant</mode_slug>
<reason>开发量化交易策略</reason>
</switch_mode>
```

### 2. 生成策略

使用 `generate_strategy` 工具将投资理念转换为策略代码：

```xml
<generate_strategy>
<investment_idea>科技股在财报季通常表现出强劲的动量效应，特别是在超预期财报发布后的2-3个月内会持续跑赢大盘</investment_idea>
<strategy_type>momentum</strategy_type>
<timeframe>daily</timeframe>
<risk_level>medium</risk_level>
</generate_strategy>
```

### 3. 回测策略

使用 `backtest_strategy` 工具测试策略的历史表现：

```xml
<backtest_strategy>
<strategy_code>[生成的策略代码]</strategy_code>
<start_date>2023-01-01</start_date>
<end_date>2024-01-01</end_date>
<initial_capital>100000</initial_capital>
<benchmark>QQQ</benchmark>
</backtest_strategy>
```

### 4. 计算性能指标

使用 `calculate_metrics` 工具获得详细的风险收益分析：

```xml
<calculate_metrics>
<returns_data>[回测返回的收益数据]</returns_data>
<benchmark_data>[基准收益数据]</benchmark_data>
<risk_free_rate>0.02</risk_free_rate>
</calculate_metrics>
```

### 5. 优化参数

使用 `optimize_parameters` 工具找到最优的策略参数：

```xml
<optimize_parameters>
<strategy_code>[策略代码]</strategy_code>
<parameter_ranges>{"sma_short": {"min": 5, "max": 20, "step": 1}, "sma_long": {"min": 20, "max": 100, "step": 5}}</parameter_ranges>
<optimization_metric>sharpe_ratio</optimization_metric>
<constraints>max_drawdown > -0.15</constraints>
</optimize_parameters>
```

## 支持的策略类型

### 1. 动量策略 (momentum)
- **适用场景**: 趋势跟踪、突破交易
- **核心指标**: 移动平均线、RSI、MACD
- **风险因素**: 趋势反转、假突破

### 2. 均值回归策略 (mean_reversion)
- **适用场景**: 震荡市场、超买超卖
- **核心指标**: 布林带、Z-score、随机指标
- **风险因素**: 趋势市场、结构性变化

### 3. 套利策略 (arbitrage)
- **适用场景**: 价差交易、配对交易
- **核心指标**: 价差、相关性、协整
- **风险因素**: 执行风险、模型失效

### 4. 因子策略 (factor_based)
- **适用场景**: 多因子模型、风格轮动
- **核心指标**: 因子载荷、因子动量
- **风险因素**: 因子拥挤、制度变化

## 风险管理框架

### 波动率目标 (Volatility Targeting)
```python
target_volatility = 0.15  # 15% 年化波动率目标
position_size = target_volatility / realized_volatility
```

### 凯利公式 (Kelly Criterion)
```python
kelly_fraction = win_rate - (1 - win_rate) * avg_win / avg_loss
position_size = kelly_fraction * 0.25  # 保守凯利
```

### 风险平价 (Risk Parity)
```python
# 等风险贡献的投资组合构建
weights = optimize_risk_parity(covariance_matrix)
```

## 性能指标说明

### 收益指标
- **总收益率**: 策略期间的累计收益
- **年化收益率**: 按年化计算的收益率
- **波动率**: 收益率的标准差

### 风险调整指标
- **夏普比率**: (收益率 - 无风险利率) / 波动率
- **索提诺比率**: (收益率 - 无风险利率) / 下行波动率
- **卡尔马比率**: 年化收益率 / 最大回撤

### 风险指标
- **最大回撤**: 从峰值到谷值的最大损失
- **VaR**: 在给定置信水平下的最大可能损失
- **CVaR**: 超过VaR的条件期望损失

### 基准相对指标
- **Alpha**: 相对基准的超额收益
- **Beta**: 相对基准的系统性风险
- **信息比率**: 超额收益 / 跟踪误差

## 最佳实践

### 1. 策略开发流程
1. **明确投资理念** - 清晰表达市场假设和预期
2. **选择合适类型** - 根据理念选择策略类型
3. **设定风险水平** - 根据风险承受能力设定参数
4. **生成初始策略** - 使用AI生成基础策略框架
5. **回测验证** - 在历史数据上验证策略有效性
6. **风险分析** - 详细分析风险收益特征
7. **参数优化** - 系统性优化策略参数
8. **样本外测试** - 在新数据上验证策略稳健性

### 2. 风险控制要点
- **分散化**: 避免过度集中在单一资产或因子
- **仓位管理**: 根据波动率动态调整仓位大小
- **止损机制**: 设置合理的止损和止盈点位
- **压力测试**: 在极端市场条件下测试策略表现

### 3. 常见陷阱
- **过度拟合**: 避免在历史数据上过度优化
- **前瞻偏差**: 确保不使用未来信息
- **生存偏差**: 考虑退市股票的影响
- **交易成本**: 充分考虑手续费和冲击成本

## 示例工作流程

```
1. 投资理念 → 2. 策略生成 → 3. 回测分析 → 4. 风险评估 → 5. 参数优化
     ↓              ↓              ↓              ↓              ↓
"科技股动量"   →  Python策略代码  →  历史表现数据  →  风险指标计算  →  最优参数组合
```

## 技术支持

如果在使用过程中遇到问题，可以：

1. **查看工具描述** - 每个工具都有详细的参数说明
2. **参考示例** - 使用提供的示例作为起点
3. **逐步调试** - 分步骤验证每个环节的输出
4. **调整参数** - 根据具体需求调整策略参数

## 更新日志

- **v1.0.0** - 初始版本，支持四种基础策略类型
- 支持动量、均值回归、套利、因子策略
- 完整的回测和风险分析框架
- 系统性参数优化功能

---

*Roo-Code 量化交易模式让量化策略开发变得简单高效。从投资理念到可执行策略，只需几个简单的工具调用。*
