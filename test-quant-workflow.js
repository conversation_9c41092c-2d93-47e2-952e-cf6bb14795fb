#!/usr/bin/env node

/**
 * 量化模式工作流程验证脚本
 * 
 * 这个脚本模拟用户的完整使用流程，验证量化模式是否可以正常运行
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 开始验证量化模式工作流程');
console.log('=' .repeat(60));

// 模拟用户输入示例
const userInputExamples = {
    // 步骤1：切换模式
    switchMode: {
        tool: "switch_mode",
        params: {
            mode_slug: "quant",
            reason: "我想开发一个基于科技股财报季动量效应的量化策略"
        }
    },

    // 步骤2：生成策略
    generateStrategy: {
        tool: "generate_strategy", 
        params: {
            investment_idea: "我观察到科技股在财报季通常表现出强劲的动量效应，特别是那些超预期财报发布后的股票，在接下来的2-3个月内往往会持续跑赢大盘。我想利用这个现象，结合技术指标和成交量确认，构建一个动量策略。",
            strategy_type: "momentum",
            timeframe: "daily", 
            risk_level: "medium"
        }
    },

    // 步骤3：回测策略
    backtestStrategy: {
        tool: "backtest_strategy",
        params: {
            strategy_code: `
class TechEarningsMomentumStrategy:
    def __init__(self):
        self.short_ma = 10
        self.long_ma = 30
        self.rsi_period = 14
        self.volume_threshold = 1.5
        
    def generate_signals(self, data):
        # 计算技术指标
        data['SMA_short'] = data['close'].rolling(self.short_ma).mean()
        data['SMA_long'] = data['close'].rolling(self.long_ma).mean()
        data['RSI'] = calculate_rsi(data['close'], self.rsi_period)
        data['volume_ratio'] = data['volume'] / data['volume'].rolling(20).mean()
        
        # 生成信号
        data['signal'] = 0
        data.loc[(data['SMA_short'] > data['SMA_long']) & 
                 (data['RSI'] > 50) & 
                 (data['volume_ratio'] > self.volume_threshold), 'signal'] = 1
        data.loc[(data['SMA_short'] < data['SMA_long']) | 
                 (data['RSI'] < 30), 'signal'] = -1
        
        return data
`,
            start_date: "2022-01-01",
            end_date: "2024-01-01", 
            initial_capital: 100000,
            benchmark: "QQQ"
        }
    },

    // 步骤4：计算指标
    calculateMetrics: {
        tool: "calculate_metrics",
        params: {
            returns_data: JSON.stringify([0.012, -0.008, 0.025, 0.018, -0.015, 0.032, 0.009, -0.012, 0.021, 0.007]),
            benchmark_data: JSON.stringify([0.008, -0.005, 0.018, 0.012, -0.010, 0.022, 0.006, -0.008, 0.015, 0.004]),
            risk_free_rate: 0.02
        }
    },

    // 步骤5：优化参数
    optimizeParameters: {
        tool: "optimize_parameters",
        params: {
            strategy_code: "# 策略代码（简化版）",
            parameter_ranges: JSON.stringify({
                "short_ma": {"min": 5, "max": 20, "step": 1},
                "long_ma": {"min": 20, "max": 100, "step": 5},
                "rsi_threshold": {"min": 60, "max": 80, "step": 2}
            }),
            optimization_metric: "sharpe_ratio",
            constraints: "max_drawdown > -0.15"
        }
    }
};

// 验证工具可用性
function verifyToolAvailability() {
    console.log('\n📋 验证工具可用性');
    console.log('-'.repeat(40));

    const toolFiles = [
        'src/core/tools/generateStrategyTool.ts',
        'src/core/tools/backtestStrategyTool.ts', 
        'src/core/tools/calculateMetricsTool.ts',
        'src/core/tools/optimizeParametersTool.ts'
    ];

    let allToolsAvailable = true;
    
    toolFiles.forEach(toolFile => {
        if (fs.existsSync(toolFile)) {
            console.log(`✅ ${path.basename(toolFile)} - 可用`);
        } else {
            console.log(`❌ ${path.basename(toolFile)} - 缺失`);
            allToolsAvailable = false;
        }
    });

    return allToolsAvailable;
}

// 验证模式配置
function verifyModeConfiguration() {
    console.log('\n📋 验证量化模式配置');
    console.log('-'.repeat(40));

    try {
        const modesContent = fs.readFileSync('src/shared/modes.ts', 'utf8');
        
        if (modesContent.includes('slug: "quant"')) {
            console.log('✅ 量化模式配置 - 已找到');
        } else {
            console.log('❌ 量化模式配置 - 未找到');
            return false;
        }

        if (modesContent.includes('"quant-tools"')) {
            console.log('✅ 量化工具组 - 已配置');
        } else {
            console.log('❌ 量化工具组 - 未配置');
            return false;
        }

        return true;
    } catch (error) {
        console.log(`❌ 读取模式配置失败: ${error.message}`);
        return false;
    }
}

// 验证工具路由
function verifyToolRouting() {
    console.log('\n📋 验证工具路由配置');
    console.log('-'.repeat(40));

    try {
        const routingContent = fs.readFileSync('src/core/assistant-message/presentAssistantMessage.ts', 'utf8');
        
        const requiredCases = [
            'case "generate_strategy"',
            'case "backtest_strategy"', 
            'case "calculate_metrics"',
            'case "optimize_parameters"'
        ];

        let allRoutesFound = true;
        requiredCases.forEach(caseStr => {
            if (routingContent.includes(caseStr)) {
                console.log(`✅ ${caseStr} - 路由已配置`);
            } else {
                console.log(`❌ ${caseStr} - 路由缺失`);
                allRoutesFound = false;
            }
        });

        return allRoutesFound;
    } catch (error) {
        console.log(`❌ 读取路由配置失败: ${error.message}`);
        return false;
    }
}

// 显示用户输入示例
function showUserInputExamples() {
    console.log('\n📋 用户输入示例');
    console.log('-'.repeat(40));

    Object.entries(userInputExamples).forEach(([stepName, example]) => {
        console.log(`\n🔸 ${stepName}:`);
        console.log(`工具: ${example.tool}`);
        console.log('参数:');
        Object.entries(example.params).forEach(([key, value]) => {
            const displayValue = typeof value === 'string' && value.length > 100 
                ? value.substring(0, 100) + '...' 
                : value;
            console.log(`  ${key}: ${displayValue}`);
        });
    });
}

// 生成XML格式示例
function generateXMLExamples() {
    console.log('\n📋 XML格式使用示例');
    console.log('-'.repeat(40));

    console.log('\n🔸 步骤1 - 切换到量化模式:');
    console.log(`<switch_mode>
<mode_slug>quant</mode_slug>
<reason>我想开发一个基于科技股财报季动量效应的量化策略</reason>
</switch_mode>`);

    console.log('\n🔸 步骤2 - 生成策略:');
    console.log(`<generate_strategy>
<investment_idea>我观察到科技股在财报季通常表现出强劲的动量效应，特别是那些超预期财报发布后的股票，在接下来的2-3个月内往往会持续跑赢大盘。我想利用这个现象，结合技术指标和成交量确认，构建一个动量策略。</investment_idea>
<strategy_type>momentum</strategy_type>
<timeframe>daily</timeframe>
<risk_level>medium</risk_level>
</generate_strategy>`);

    console.log('\n🔸 步骤3 - 回测策略:');
    console.log(`<backtest_strategy>
<strategy_code>[生成的策略代码]</strategy_code>
<start_date>2022-01-01</start_date>
<end_date>2024-01-01</end_date>
<initial_capital>100000</initial_capital>
<benchmark>QQQ</benchmark>
</backtest_strategy>`);

    console.log('\n🔸 步骤4 - 计算指标:');
    console.log(`<calculate_metrics>
<returns_data>[回测返回的收益数据]</returns_data>
<benchmark_data>[基准收益数据]</benchmark_data>
<risk_free_rate>0.02</risk_free_rate>
</calculate_metrics>`);

    console.log('\n🔸 步骤5 - 优化参数:');
    console.log(`<optimize_parameters>
<strategy_code>[策略代码]</strategy_code>
<parameter_ranges>{"short_ma": {"min": 5, "max": 20, "step": 1}, "long_ma": {"min": 20, "max": 100, "step": 5}}</parameter_ranges>
<optimization_metric>sharpe_ratio</optimization_metric>
<constraints>max_drawdown > -0.15</constraints>
</optimize_parameters>`);
}

// 主验证函数
async function runWorkflowVerification() {
    const verificationSteps = [
        { name: '工具可用性', fn: verifyToolAvailability },
        { name: '模式配置', fn: verifyModeConfiguration },
        { name: '工具路由', fn: verifyToolRouting }
    ];

    let allPassed = true;
    for (const step of verificationSteps) {
        if (!step.fn()) {
            allPassed = false;
        }
    }

    showUserInputExamples();
    generateXMLExamples();

    console.log('\n' + '='.repeat(60));
    if (allPassed) {
        console.log('🎉 量化模式工作流程验证通过！');
        console.log('✨ 您现在可以使用上述XML示例来测试量化模式功能。');
    } else {
        console.log('⚠️  验证发现问题，请检查上述错误信息。');
    }
    console.log('='.repeat(60));
}

// 运行验证
runWorkflowVerification().catch(console.error);
